/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Chart: typeof import('./../components/Chart/index.vue')['default']
    GhostButton: typeof import('./../components/GhostButton.vue')['default']
    NavBar: typeof import('./../components/NavBar.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TabBar: typeof import('./../components/TabBar.vue')['default']
    VanButton: typeof import('vant/es')['Button']
    VanCell: typeof import('vant/es')['Cell']
    VanCellGroup: typeof import('vant/es')['CellGroup']
    VanConfigProvider: typeof import('vant/es')['ConfigProvider']
    VanEmpty: typeof import('vant/es')['Empty']
    VanField: typeof import('vant/es')['Field']
    VanForm: typeof import('vant/es')['Form']
    VanIcon: typeof import('vant/es')['Icon']
    VanImage: typeof import('vant/es')['Image']
    VanList: typeof import('vant/es')['List']
    VanNavBar: typeof import('vant/es')['NavBar']
    VanPicker: typeof import('vant/es')['Picker']
    VanPopup: typeof import('vant/es')['Popup']
    VanSpace: typeof import('vant/es')['Space']
    VanStepper: typeof import('vant/es')['Stepper']
    VanSwitch: typeof import('vant/es')['Switch']
    VanTabbar: typeof import('vant/es')['Tabbar']
    VanTabbarItem: typeof import('vant/es')['TabbarItem']
    VanTextEllipsis: typeof import('vant/es')['TextEllipsis']
  }
}
