import { ClientResponse, ClientResponseCode, ClientResponseDetailCode } from '@moxo/proto';

/**
 * Business server error class that extends the standard Error
 * Contains server response information for better error handling
 */
export class BizServerError extends Error {
  public readonly code?: ClientResponseCode | null;
  public readonly detailCode?: ClientResponseDetailCode | null;
  public readonly serverMessage?: string;
  public readonly response?: ClientResponse | null;

  constructor(message: string, response: ClientResponse, props?: Record<string, boolean>) {
    super(message);
    this.name = 'BizServerError';
    this.code = response.code;
    this.detailCode = response.detail_code;
    this.serverMessage = response.message;
    this.response = response;
    if (props) {
      Object.assign(this, props);
    }
    // Maintains proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, BizServerError);
    }
  }
}
export function isBizServerError(error: any): error is BizServerError {
  return (
    error &&
    typeof error === 'object' &&
    error.name === 'BizServerError' &&
    typeof error.message === 'string'
  );
}
export function convertToBizServerError(response: ClientResponse): BizServerError {
  return new BizServerError(response.message || '', response);
}
export function buildServerError(message: string, type: ClientResponseCode): BizServerError {
  return new BizServerError(message, {
    code: type,
  });
}
