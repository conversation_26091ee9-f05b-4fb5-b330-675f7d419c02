"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Thumbnail = void 0;
// Thumbnail 组件导出
var thumbnail_vue_1 = require("./thumbnail.vue");
Object.defineProperty(exports, "Thumbnail", { enumerable: true, get: function () { return __importDefault(thumbnail_vue_1).default; } });
// 如果有其他相关的类型或工具函数，也可以在这里导出
// export type { ThumbnailProps } from './types'
// export { thumbnailUtils } from './utils'
