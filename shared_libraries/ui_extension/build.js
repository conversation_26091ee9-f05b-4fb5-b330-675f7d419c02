import { build } from 'vite'
import { resolve } from 'path'
import { readdirSync, statSync, writeFileSync, readFileSync } from 'fs'
import { fileURLToPath } from 'url'

const __dirname = fileURLToPath(new URL('.', import.meta.url))

// 动态获取 src 目录下的所有子目录
function getSubDirectories(dir) {
  try {
    return readdirSync(dir).filter(item => {
      const fullPath = resolve(dir, item)
      return statSync(fullPath).isDirectory()
    })
  } catch (error) {
    return []
  }
}

// 获取 src 下的所有子目录
const srcDir = resolve(__dirname, 'src')
const subDirectories = getSubDirectories(srcDir)

// 构建入口点配置
const entries = {
  // 主入口
  index: resolve(__dirname, 'src/index.ts')
}

// 为每个子目录添加入口点
subDirectories.forEach(dir => {
  entries[dir] = resolve(__dirname, `src/${dir}/index.ts`)
})

console.log('Building with entries:', Object.keys(entries))

// 更新 package.json 的 exports 字段
const packageJsonPath = resolve(__dirname, 'package.json')
const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf-8'))

// 动态生成 exports
const exports = {
  ".": {
    "types": "./lib/types/index.d.ts",
    "import": "./lib/es/index.js",
    "require": "./lib/cjs/index.cjs"
  }
}

subDirectories.forEach(dir => {
  exports[`./${dir}`] = {
    "types": `./lib/types/${dir}/index.d.ts`,
    "import": `./lib/es/${dir}/index.js`,
    "require": `./lib/cjs/${dir}/index.cjs`
  }
})

packageJson.exports = exports
writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2))

console.log('Updated package.json exports:', Object.keys(exports))

// 执行构建
async function buildLibrary() {
  try {
    // 导入 vite 配置
    const { default: config } = await import('./vite.config.ts')
    
    // 更新配置中的入口点
    config.build.lib.entry = entries
    
    await build(config)
    console.log('Build completed successfully!')
  } catch (error) {
    console.error('Build failed:', error)
    process.exit(1)
  }
}

buildLibrary()
