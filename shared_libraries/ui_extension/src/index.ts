// 主入口文件 - 导出所有模块

// Thumbnail 模块
export * from './thumbnail/index'

// 如果将来有更多模块，可以在这里添加
// export * from './other-module'

// 也可以提供一个默认的安装函数供 Vue 应用使用
import type { App } from 'vue'
import { Thumbnail } from './thumbnail/index'

export const components = {
  Thumbnail
}

export const install = (app: App) => {
  Object.entries(components).forEach(([name, component]) => {
    app.component(name, component)
  })
}

export default {
  install,
  ...components
}
