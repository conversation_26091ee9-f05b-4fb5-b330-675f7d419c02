{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM"], "module": "ESNext", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": false, "strict": true, "noEmit": false, "declaration": true, "outDir": "lib", "rootDir": "src", "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "jsx": "preserve"}, "include": ["src/**/*"], "exclude": ["node_modules", "lib", "dist", "**/*.test.*", "**/*.spec.*"]}