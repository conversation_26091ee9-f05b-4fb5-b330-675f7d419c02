# @moxo/ui-ext

基于 Vue 3 和 Vite 的 UI 扩展组件库，支持按模块单独导入。

## 安装

```bash
pnpm add @moxo/ui-ext
```

## 使用方式

### 1. 完整导入

```typescript
import { install, Thumbnail } from '@moxo/ui-ext'
import { createApp } from 'vue'

const app = createApp({})

// 安装所有组件
app.use(install)

// 或者单独注册组件
app.component('Thumbnail', Thumbnail)
```

### 2. 按需导入（推荐）

```typescript
// 导入特定模块
import { Thumbnail } from '@moxo/ui-ext/thumbnail'

// 在组件中使用
export default {
  components: {
    Thumbnail
  }
}
```

### 3. 在 Vue SFC 中使用

```vue
<template>
  <div>
    <Thumbnail />
  </div>
</template>

<script setup lang="ts">
import { Thumbnail } from '@moxo/ui-ext/thumbnail'
</script>
```

## 开发

### 构建

```bash
# 构建所有格式
pnpm build

# 只构建 ES 模块
pnpm build:es

# 开发模式（监听文件变化）
pnpm dev
```

### 添加新模块

1. 在 `src/` 目录下创建新的子目录
2. 在子目录中创建组件文件
3. 在子目录中创建 `index.ts` 导出文件
4. 运行构建命令，`package.json` 的 `exports` 字段会自动更新

### 项目结构

```
src/
├── index.ts          # 主入口文件
├── thumbnail/        # Thumbnail 模块
│   ├── index.ts      # 模块导出
│   └── thumbnail.vue # 组件实现
└── [other-modules]/  # 其他模块...
```

## 构建输出

构建后会生成以下文件：

```
lib/
├── es/              # ES 模块格式
│   ├── index.js
│   └── thumbnail/
│       └── index.js
├── cjs/             # CommonJS 格式
│   ├── index.cjs
│   └── thumbnail/
│       └── index.cjs
└── types/           # TypeScript 类型声明
    ├── index.d.ts
    └── thumbnail/
        └── index.d.ts
```

## 特性

- ✅ 支持 Vue 3
- ✅ TypeScript 支持
- ✅ 按需导入
- ✅ ES 模块和 CommonJS 双格式输出
- ✅ 自动生成类型声明
- ✅ 动态模块发现和导出
