import { execSync } from 'child_process'
import { resolve } from 'path'
import { readdirSync, statSync, writeFileSync, readFileSync, mkdirSync, existsSync } from 'fs'
import { fileURLToPath } from 'url'

const __dirname = fileURLToPath(new URL('.', import.meta.url))

// 动态获取 src 目录下的所有子目录
function getSubDirectories(dir) {
  try {
    return readdirSync(dir).filter(item => {
      const fullPath = resolve(dir, item)
      return statSync(fullPath).isDirectory()
    })
  } catch (error) {
    return []
  }
}

// 获取 src 下的所有子目录
const srcDir = resolve(__dirname, 'src')
const subDirectories = getSubDirectories(srcDir)

console.log('Found subdirectories:', subDirectories)

// 更新 package.json 的 exports 字段
const packageJsonPath = resolve(__dirname, 'package.json')
const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf-8'))

// 动态生成 exports
const exports = {
  ".": {
    "types": "./lib/types/index.d.ts",
    "import": "./lib/es/index.js",
    "require": "./lib/cjs/index.cjs"
  }
}

subDirectories.forEach(dir => {
  exports[`./${dir}`] = {
    "types": `./lib/types/${dir}/index.d.ts`,
    "import": `./lib/es/${dir}/index.js`,
    "require": `./lib/cjs/${dir}/index.cjs`
  }
})

packageJson.exports = exports
writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2))

console.log('Updated package.json exports:', Object.keys(exports))

// 创建 lib 目录结构
const libDir = resolve(__dirname, 'lib')
const esDir = resolve(libDir, 'es')
const cjsDir = resolve(libDir, 'cjs')
const typesDir = resolve(libDir, 'types')

if (!existsSync(libDir)) mkdirSync(libDir, { recursive: true })
if (!existsSync(esDir)) mkdirSync(esDir, { recursive: true })
if (!existsSync(cjsDir)) mkdirSync(cjsDir, { recursive: true })
if (!existsSync(typesDir)) mkdirSync(typesDir, { recursive: true })

// 使用 TypeScript 编译器生成 ES 模块
console.log('Building ES modules...')
try {
  execSync('npx tsc -p tsconfig.json --module esnext --outDir lib/es --declaration --declarationDir lib/types', {
    cwd: __dirname,
    stdio: 'inherit'
  })
  console.log('ES modules built successfully')
} catch (error) {
  console.error('Failed to build ES modules:', error.message)
}

// 使用 TypeScript 编译器生成 CommonJS 模块
console.log('Building CommonJS modules...')
try {
  execSync('npx tsc -p tsconfig.json --module commonjs --outDir lib/cjs', {
    cwd: __dirname,
    stdio: 'inherit'
  })
  console.log('CommonJS modules built successfully')
} catch (error) {
  console.error('Failed to build CommonJS modules:', error.message)
}

// 重命名 CommonJS 文件为 .cjs 扩展名
console.log('Renaming CommonJS files...')
function renameJsToCjs(dir) {
  const items = readdirSync(dir)
  items.forEach(item => {
    const fullPath = resolve(dir, item)
    if (statSync(fullPath).isDirectory()) {
      renameJsToCjs(fullPath)
    } else if (item.endsWith('.js')) {
      const newPath = fullPath.replace(/\.js$/, '.cjs')
      execSync(`mv "${fullPath}" "${newPath}"`)
    }
  })
}

try {
  renameJsToCjs(cjsDir)
  console.log('CommonJS files renamed successfully')
} catch (error) {
  console.error('Failed to rename CommonJS files:', error.message)
}

console.log('Build completed!')
