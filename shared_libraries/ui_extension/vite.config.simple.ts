import { defineConfig } from 'vite'
import { resolve } from 'path'
import { readdirSync, statSync } from 'fs'

// 动态获取 src 目录下的所有子目录
function getSubDirectories(dir: string): string[] {
  try {
    return readdirSync(dir).filter(item => {
      const fullPath = resolve(dir, item)
      return statSync(fullPath).isDirectory()
    })
  } catch (error) {
    return []
  }
}

// 获取 src 下的所有子目录
const srcDir = resolve(__dirname, 'src')
const subDirectories = getSubDirectories(srcDir)

// 构建入口点配置
const entries: Record<string, string> = {
  // 主入口
  index: resolve(__dirname, 'src/index.ts')
}

// 为每个子目录添加入口点
subDirectories.forEach(dir => {
  entries[dir] = resolve(__dirname, `src/${dir}/index.ts`)
})

export default defineConfig({
  build: {
    lib: {
      entry: entries,
      name: 'MoxoUIExt',
      formats: ['es', 'cjs']
    },
    rollupOptions: {
      // 确保外部化处理那些你不想打包进库的依赖
      external: ['vue', 'uuid', 'type-fest'],
      output: [
        {
          format: 'es',
          dir: 'lib/es',
          entryFileNames: '[name].js',
          chunkFileNames: 'chunks/[name]-[hash].js',
          preserveModules: true,
          preserveModulesRoot: 'src'
        },
        {
          format: 'cjs',
          dir: 'lib/cjs',
          entryFileNames: '[name].cjs',
          chunkFileNames: 'chunks/[name]-[hash].cjs',
          preserveModules: true,
          preserveModulesRoot: 'src'
        }
      ]
    },
    outDir: 'lib',
    emptyOutDir: true
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  }
})
